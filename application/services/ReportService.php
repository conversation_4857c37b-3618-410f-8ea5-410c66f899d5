<?php
defined('BASEPATH') || exit('No direct script access allowed');

require_once 'vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class ReportService {
    private $CI;

    public function __construct() {
        $this->CI = &get_instance();
    }

    /**
     * Extrai informações de erro de um registro
     *
     * @param mixed $error Registro de erro
     * @return array Informações extraídas (part_number, estabelecimento, linha, coluna, mensagem)
     */
    private function extractErrorInfo($error) {

        // Inicializar informações
        $info = [
            'part_number' => 'N/A',
            'estabelecimento' => 'N/A',
            'linha' => 'N/A',
            'coluna' => 'N/A',
            'mensagem' => 'Erro não especificado'
        ];
        
        if (is_array($error) && isset($error['com_erro']) && is_array($error['com_erro'])) {
            // Pegar o primeiro erro do array com_erro
            if (!empty($error['com_erro'][0])) {
                $error = $error['com_erro'][0];
            }
        }
        
        if (is_array($error)) {
            // Extrair informações do array
            if (isset($error['part_number'])) {
                $info['part_number'] = $error['part_number'];
            }
            
            if (isset($error['estabelecimento'])) {
                $info['estabelecimento'] = $error['estabelecimento'];
            }
            
            if (isset($error['linha'])) {
                $info['linha'] = $error['linha'];
            }
            
            if (isset($error['coluna'])) {
                $info['coluna'] = $error['coluna'];
            }
            
            if (isset($error['mensagem'])) {
                $info['mensagem'] = $error['mensagem'];
            }
        } elseif (is_string($error)) {
            // Tentar extrair informações da string
            $info['mensagem'] = $error;
            
            // Extrair part_number e estabelecimento
            if (preg_match('/Part number \[<b>(.*?)<\/b>\]\[<b>(.*?)<\/b>\]/', $error, $matches)) {
                $info['part_number'] = $matches[1];
                $info['estabelecimento'] = $matches[2];
            } elseif (preg_match('/Part number \[<b>(.*?)<\/b>\]/', $error, $matches)) {
                $info['part_number'] = $matches[1];
            }
            
            // Extrair estabelecimento de outros padrões
            if ($info['estabelecimento'] === 'N/A' && preg_match('/estabelecimento\s+<b>(.*?)<\/b>/i', $error, $matches)) {
                $info['estabelecimento'] = $matches[1];
            } elseif ($info['estabelecimento'] === 'N/A' && preg_match('/estabelecimento\s+(.*?)(?:\s|$)/i', $error, $matches)) {
                $info['estabelecimento'] = $matches[1];
            }
            
            // Extrair linha
            if (preg_match('/linha\s+<b>(\d+)<\/b>/', $error, $matches) || 
                preg_match('/linha\s+(\d+)/', $error, $matches)) {
                $info['linha'] = $matches[1];
            }
            
            // Extrair coluna
            if (preg_match('/coluna\s+<b>([A-Z]+)<\/b>/', $error, $matches) || 
                preg_match('/coluna\s+([A-Z]+)/', $error, $matches)) {
                $info['coluna'] = $matches[1];
            }
        }
        
        // Garantir que a mensagem seja uma string
        if (is_string($info['mensagem'])) {
            $info['mensagem'] = strip_tags($info['mensagem']);
        } elseif (is_array($info['mensagem'])) {
            $info['mensagem'] = strip_tags(implode(', ', $info['mensagem']));
        } else {
            $info['mensagem'] = 'Erro não especificado';
        }

        return $info;
    }

    /**
     * Gera um relatório de erros em formato Excel
     *
     * @param array $logs Array com os logs de processamento
     * @param string $filename Nome do arquivo original
     * @param array $idx Índices das colunas
     * @return string Nome do arquivo gerado ou string vazia em caso de falha
     */
    public function generateErrorReport($logs, $original_filename) {
        // Carregar helper de arquivos
        $this->CI->load->helper('file');
        
        // Criar e configurar a planilha
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // Configurar cabeçalho
        $sheet->setCellValue('A1', 'Relatório de Erros - ' . $original_filename);
        $sheet->mergeCells('A1:F1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        
        // Cabeçalhos das colunas
        $sheet->setCellValue('A3', 'Tipo de Erro');
        $sheet->setCellValue('B3', 'Part Number');
        $sheet->setCellValue('C3', 'Estabelecimento');
        $sheet->setCellValue('D3', 'Linha');
        $sheet->setCellValue('E3', 'Coluna');
        $sheet->setCellValue('F3', 'Descrição do Erro');
        
        $sheet->getStyle('A3:F3')->getFont()->setBold(true);
        $sheet->getStyle('A3:F3')->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('DDDDDD');
        
        // Preencher dados
        $row = 4;
        
        // Erros de estrutura da planilha
        if (isset($logs['com_erro']['planilha_errors'])) {
            foreach ($logs['com_erro']['planilha_errors'] as $error) {
                $errorInfo = $this->extractErrorInfo($error);
                
                $sheet->setCellValue('A' . $row, 'Estrutura da Planilha');
                $sheet->setCellValue('B' . $row, $errorInfo['part_number']);
                $sheet->setCellValue('C' . $row, $errorInfo['estabelecimento']);
                $sheet->setCellValue('D' . $row, $errorInfo['linha']);
                $sheet->setCellValue('E' . $row, $errorInfo['coluna']);
                $sheet->setCellValue('F' . $row, $errorInfo['mensagem']);
                $row++;
            }
        }
        
        // Erros de processamento de itens (excluindo planilha_errors)
        foreach ($logs['com_erro'] as $key => $error) {
            // Pular o array planilha_errors que já foi processado
            if ($key === 'planilha_errors') {
                continue;
            }
            
            $errorInfo = $this->extractErrorInfo($error);
            
            $sheet->setCellValue('A' . $row, 'Processamento de Item');
            $sheet->setCellValue('B' . $row, $errorInfo['part_number']);
            $sheet->setCellValue('C' . $row, $errorInfo['estabelecimento']);
            $sheet->setCellValue('D' . $row, $errorInfo['linha']);
            $sheet->setCellValue('E' . $row, $errorInfo['coluna']);
            $sheet->setCellValue('F' . $row, $errorInfo['mensagem']);
            $row++;
        }
        
        // Ajustar largura das colunas
        $sheet->getColumnDimension('A')->setWidth(20);
        $sheet->getColumnDimension('B')->setWidth(20);
        $sheet->getColumnDimension('C')->setWidth(20);
        $sheet->getColumnDimension('D')->setWidth(10);
        $sheet->getColumnDimension('E')->setWidth(15);
        $sheet->getColumnDimension('F')->setWidth(60);
        
        // Encontrar um diretório gravável
        $dir = find_writable_directory();
        if (empty($dir)) {
            log_message('error', 'Não foi possível encontrar um diretório gravável para salvar o relatório');
            return '';
        }
        
        // Salvar arquivo
        $filename = 'erro_importacao_' . date('YmdHis') . '.xlsx';
        $filepath = $dir . $filename;
        
        try {
            $writer = new Xlsx($spreadsheet);
            $writer->save($filepath);
            
            // Verificar se o arquivo foi criado
            if (!file_exists($filepath)) {
                log_message('error', 'Arquivo não foi criado: ' . $filepath);
                return '';
            }
            
            // Garantir permissões corretas
            @chmod($filepath, 0666);
            
            // Armazenar o diretório usado para referência futura
            $this->CI->session->set_userdata('last_report_directory', $dir);
            
            return $filename;
        } catch (Exception $e) {
            log_message('error', 'Erro ao salvar relatório: ' . $e->getMessage());
            return '';
        }
    }
}
